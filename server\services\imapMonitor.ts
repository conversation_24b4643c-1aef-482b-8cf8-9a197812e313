import Imap from 'imap';
import { simple<PERSON><PERSON><PERSON>, ParsedMail } from 'mailparser';

class ImapMonitorService {
  private imap: any;
  private isConnected: boolean = false;

  constructor() {
    this.imap = new Imap({
      user: process.env.GMAIL_EMAIL!,
      password: process.env.GMAIL_APP_PASSWORD!,
      host: 'imap.gmail.com',
      port: 993,
      tls: true,
      tlsOptions: { rejectUnauthorized: false }
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    this.imap.once('ready', () => {
      console.log('\x1b[32m📧 IMAP connection ready\x1b[0m');
      this.isConnected = true;
      this.openInbox();
    });

    this.imap.once('error', (err: Error) => {
      console.error('\x1b[31m❌ IMAP connection error:\x1b[0m', err.message);
    });

    this.imap.once('end', () => {
      console.log('\x1b[33m📪 IMAP connection ended\x1b[0m');
      this.isConnected = false;
    });
  }

  start(): void {
    console.log('\x1b[36m🔍 Starting IMAP email monitor...\x1b[0m');
    this.imap.connect();
  }

  stop(): void {
    console.log('\x1b[33m⏹️  Stopping IMAP monitor...\x1b[0m');
    if (this.isConnected) {
      this.imap.end();
    }
    // Force close after timeout
    setTimeout(() => {
      if (this.isConnected) {
        console.log('\x1b[31m🔌 Force closing IMAP connection...\x1b[0m');
        this.imap.destroy();
      }
    }, 2000);
  }

  private openInbox(): void {
    this.imap.openBox('INBOX', false, (err: any, box: any) => {
      if (err) {
        console.error('Error opening inbox:', err);
        return;
      }

      console.log(`\x1b[36m📬 Monitoring inbox with ${box.messages.total} messages\x1b[0m`);
      this.processExistingUnreadMessages();
      this.watchForNewMessages();
    });
  }

  private watchForNewMessages(): void {
    this.imap.on('mail', (numNewMsgs: number) => {
      console.log(`\x1b[32m✉️  ${numNewMsgs} new message(s) received\x1b[0m`);
      this.fetchNewMessages();
    });
  }

  private processExistingUnreadMessages(): void {
    this.imap.search(['UNSEEN'], (err: any, results: any) => {
      if (err) {
        console.error('Error searching for unread messages:', err);
        return;
      }

      if (results.length > 0) {
        console.log(`\x1b[35m📮 Found ${results.length} existing unread messages\x1b[0m`);
        this.fetchMessages(results);
      }
    });
  }

  private fetchNewMessages(): void {
    const fetch = this.imap.seq.fetch('*:*', {
      bodies: '',
      markSeen: false
    });

    fetch.on('message', (msg: any, seqno: any) => {
      this.processMessage(msg, seqno);
    });

    fetch.once('error', (err: any) => {
      console.error('Fetch error:', err);
    });
  }

  private fetchMessages(uids: number[]): void {
    const fetch = this.imap.fetch(uids, {
      bodies: '',
      markSeen: true
    });

    fetch.on('message', (msg: any, seqno: any) => {
      this.processMessage(msg, seqno);
    });

    fetch.once('error', (err: any) => {
      console.error('Fetch error:', err);
    });
  }

  private processMessage(msg: any, seqno: any): void {
    msg.on('body', (stream: any) => {
      simpleParser(stream, (err: any, parsed: ParsedMail) => {
        if (err) {
          console.error('Error parsing message:', err);
          return;
        }

        console.log('\x1b[44m\x1b[37m📨 === EMAIL PROCESSED ===\x1b[0m');
        console.log(`\x1b[36mFrom:\x1b[0m ${parsed.from?.text || 'Unknown'}`);
        console.log(`\x1b[36mTo:\x1b[0m ${Array.isArray(parsed.to) ? parsed.to.map(t => t.text).join(', ') : parsed.to?.text || 'Unknown'}`);
        console.log(`\x1b[36mSubject:\x1b[0m ${parsed.subject || 'No subject'}`);
        console.log(`\x1b[36mBody:\x1b[0m ${(parsed.text || '').substring(0, 200)}...`);
        console.log(`\x1b[36mDate:\x1b[0m ${parsed.date}`);
        console.log('\x1b[44m\x1b[37m========================\x1b[0m');
      });
    });

    msg.once('attributes', (attrs: any) => {
      this.imap.addFlags(attrs.uid, '\\Seen', (err: any) => {
        if (err) console.error('Error marking as read:', err);
      });
    });
  }
}

export default ImapMonitorService;