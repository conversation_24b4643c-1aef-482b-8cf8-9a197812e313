import React from 'react';
import { styled } from '@mui/material/styles';
import { Box, Button } from '@mui/material';
import { useTranslation } from 'react-i18next';

// Styled Components
const NavLinksContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  [theme.breakpoints.down('md')]: {
    display: 'none',
  },
}));

const NavLink = styled(Button)(({ theme }) => ({
  color: 'white',
  textTransform: 'none',
  fontWeight: 500,
  fontSize: '0.95rem',
  padding: theme.spacing(0.75, 1.5),
  borderRadius: theme.spacing(2),
  minWidth: 'auto',
  transition: theme.transitions.create(['background-color', 'transform'], {
    duration: theme.transitions.duration.short,
  }),
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    transform: 'translateY(-1px)',
  },
  '&:active': {
    transform: 'translateY(0)',
  },
}));

// Types
interface NavigationItem {
  key: string;
  labelKey: string;
}

interface NavLinksProps {
  navigationItems: NavigationItem[];
  onNavClick: (section: string) => void;
}

const NavLinks: React.FC<NavLinksProps> = ({ navigationItems, onNavClick }) => {
  const { t } = useTranslation();

  return (
    <NavLinksContainer>
      {navigationItems.map((item) => (
        <NavLink
          key={item.key}
          onClick={() => onNavClick(item.key)}
        >
          {t(item.labelKey)}
        </NavLink>
      ))}
    </NavLinksContainer>
  );
};

export default NavLinks;
