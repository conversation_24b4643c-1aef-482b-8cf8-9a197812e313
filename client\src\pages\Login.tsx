import React, { useState } from 'react';
import { Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { LoginProps, AuthResponse, ApiError, LoginCredentials, RegisterCredentials } from '../types';
import {
  LoginPageContainer,
  LoginContainer,
  LoginCard,
  LoginCardContent,
  LoginHeader,
  LoginLogo,
  LoginTitle,
  LoginSubtitle,
  LoginDescription,
  LoginForm,
  LoginTextField,
  LoginSubmitButton,
  LoginAlert,
  LoginFooter,
  LoginToggleLink,
  LoginBackButton,
} from '../components/styled';

const Login: React.FC<LoginProps> = ({ onLogin, onBack }) => {
  const { t } = useTranslation();
  const [isLogin, setIsLogin] = useState<boolean>(true);
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [name, setName] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);

  const handleSubmit = async (e: React.FormEvent): Promise<void> => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const endpoint = isLogin ? '/api/auth/login' : '/api/auth/register';
      const body: LoginCredentials | RegisterCredentials = isLogin 
        ? { email, password }
        : { email, password, name };
        
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      });

      if (response.ok) {
        const data: AuthResponse = await response.json();
        onLogin(data.token, data.user);
      } else {
        const errorData: ApiError = await response.json();
        setError(errorData.error || t('auth.errorOccurred'));
      }
    } catch (err) {
      console.error('Network error:', err);
      setError(t('auth.networkError'));
    } finally {
      setLoading(false);
    }
  };

  const toggleMode = (): void => {
    setIsLogin(!isLogin);
    setError('');
  };

  return (
    <LoginPageContainer>
      <LoginContainer maxWidth="sm">
        <LoginCard>
          <LoginCardContent>
            <LoginHeader>
              <LoginLogo>
                <img src="/Pigeon Squad Logo.png" alt="Pigeon Squad" style={{ width: '100%', height: '100%' }} />
              </LoginLogo>
              <LoginTitle variant="h4">{t('app.title')}</LoginTitle>
              <LoginSubtitle variant="h5">
                {isLogin ? t('auth.welcomeBack') : t('auth.joinApp')}
              </LoginSubtitle>
              <LoginDescription variant="body2">
                {isLogin
                  ? t('auth.signInDescription')
                  : t('auth.signUpDescription')
                }
              </LoginDescription>
            </LoginHeader>

            <LoginForm as="form" onSubmit={handleSubmit}>
              {!isLogin && (
                <LoginTextField
                  fullWidth
                  label={t('auth.fullName')}
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                  autoComplete="name"
                />
              )}

              <LoginTextField
                fullWidth
                label={t('auth.emailAddress')}
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                autoComplete="email"
              />

              <LoginTextField
                fullWidth
                label={t('auth.password')}
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                autoComplete={isLogin ? "current-password" : "new-password"}
              />

              {error && (
                <LoginAlert severity="error">
                  {error}
                </LoginAlert>
              )}

              <LoginSubmitButton
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                disabled={loading}
              >
                {loading ? t('auth.pleaseWait') : (isLogin ? t('auth.signIn') : t('auth.createAccount'))}
              </LoginSubmitButton>
            </LoginForm>

            <LoginFooter>
              <Typography variant="body2">
                {isLogin ? t('auth.noAccount') : t('auth.haveAccount')}
                <LoginToggleLink onClick={toggleMode}>
                  {isLogin ? t('auth.signUp') : t('auth.signIn')}
                </LoginToggleLink>
              </Typography>

              <LoginBackButton
                onClick={onBack}
                variant="outlined"
                size="small"
              >
                {t('auth.backToHome')}
              </LoginBackButton>
            </LoginFooter>
          </LoginCardContent>
        </LoginCard>
      </LoginContainer>
    </LoginPageContainer>
  );
};

export default Login;