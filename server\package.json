{"name": "pigeon-squad-server", "version": "1.0.0", "description": "Pigeon Squad Server - Family Activity Monitor Backend", "main": "dist/index.js", "scripts": {"dev": "cross-env NODE_OPTIONS=--no-deprecation nodemon", "build": "tsc", "start": "node dist/index.js", "test": "echo \"Error: no test specified\" && exit 1", "migration:generate": "typeorm-ts-node-commonjs migration:generate -d data-source.ts", "migration:run": "typeorm-ts-node-commonjs migration:run -d data-source.ts", "migration:revert": "typeorm-ts-node-commonjs migration:revert -d data-source.ts"}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "imap": "^0.8.17", "jsonwebtoken": "^9.0.2", "mailparser": "^3.7.4", "nodemailer": "^6.9.7", "reflect-metadata": "^0.1.13", "typeorm": "^0.3.17"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/imap": "^0.8.42", "@types/jsonwebtoken": "^9.0.5", "@types/mailparser": "^3.4.6", "@types/node": "^20.10.5", "cross-env": "^10.0.0", "nodemon": "^3.0.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}