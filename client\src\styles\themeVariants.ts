import { Theme, alpha } from '@mui/material/styles';
import { extendedShadows } from './themeExtensions';

// =============================================================================
// COMPONENT VARIANTS FOR MUI THEME
// =============================================================================

/**
 * Button variants for different use cases
 */
export const buttonVariants = [
  {
    props: { variant: 'gradient' as const },
    style: (theme: Theme) => ({
      background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
      color: theme.palette.common.white,
      boxShadow: extendedShadows.card,
      '&:hover': {
        background: `linear-gradient(135deg, ${theme.palette.primary.dark}, ${theme.palette.secondary.dark})`,
        boxShadow: extendedShadows.cardHover,
        transform: 'translateY(-1px)',
      },
      '&:active': {
        transform: 'translateY(0)',
      },
    }),
  },
  {
    props: { variant: 'glass' as const },
    style: (theme: Theme) => ({
      backgroundColor: alpha(theme.palette.common.white, 0.1),
      backdropFilter: 'blur(10px)',
      border: `1px solid ${alpha(theme.palette.common.white, 0.2)}`,
      color: theme.palette.text.primary,
      '&:hover': {
        backgroundColor: alpha(theme.palette.common.white, 0.2),
        transform: 'translateY(-1px)',
      },
    }),
  },
  {
    props: { variant: 'soft' as const },
    style: (theme: Theme) => ({
      backgroundColor: alpha(theme.palette.primary.main, 0.1),
      color: theme.palette.primary.main,
      border: 'none',
      '&:hover': {
        backgroundColor: alpha(theme.palette.primary.main, 0.2),
        transform: 'translateY(-1px)',
      },
    }),
  },
];

/**
 * Card variants for different contexts
 */
export const cardVariants = [
  {
    props: { variant: 'elevated' as const },
    style: (_theme: Theme) => ({
      boxShadow: extendedShadows.cardElevated,
      '&:hover': {
        boxShadow: extendedShadows.modal,
        transform: 'translateY(-2px)',
      },
    }),
  },
  {
    props: { variant: 'glass' as const },
    style: (theme: Theme) => ({
      backgroundColor: alpha(theme.palette.background.paper, 0.8),
      backdropFilter: 'blur(10px)',
      border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
      boxShadow: 'none',
    }),
  },
  {
    props: { variant: 'gradient' as const },
    style: (theme: Theme) => ({
      background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.secondary.main, 0.05)})`,
      border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
      boxShadow: extendedShadows.card,
    }),
  },
  {
    props: { variant: 'interactive' as const },
    style: (theme: Theme) => ({
      cursor: 'pointer',
      transition: theme.transitions.create(['transform', 'box-shadow'], {
        duration: theme.transitions.duration.short,
      }),
      '&:hover': {
        transform: 'translateY(-2px)',
        boxShadow: extendedShadows.cardHover,
      },
      '&:active': {
        transform: 'translateY(-1px)',
      },
    }),
  },
];

/**
 * Chip variants for different states
 */
export const chipVariants = [
  {
    props: { variant: 'priority-high' as const },
    style: (theme: Theme) => ({
      backgroundColor: theme.palette.error.main,
      color: theme.palette.error.contrastText,
      fontWeight: 600,
      '&:hover': {
        backgroundColor: theme.palette.error.dark,
      },
    }),
  },
  {
    props: { variant: 'priority-medium' as const },
    style: (theme: Theme) => ({
      backgroundColor: theme.palette.warning.main,
      color: theme.palette.warning.contrastText,
      fontWeight: 600,
      '&:hover': {
        backgroundColor: theme.palette.warning.dark,
      },
    }),
  },
  {
    props: { variant: 'priority-low' as const },
    style: (theme: Theme) => ({
      backgroundColor: theme.palette.success.main,
      color: theme.palette.success.contrastText,
      fontWeight: 600,
      '&:hover': {
        backgroundColor: theme.palette.success.dark,
      },
    }),
  },
  {
    props: { variant: 'soft' as const },
    style: (theme: Theme) => ({
      backgroundColor: alpha(theme.palette.primary.main, 0.1),
      color: theme.palette.primary.main,
      border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
      '&:hover': {
        backgroundColor: alpha(theme.palette.primary.main, 0.2),
      },
    }),
  },
];

/**
 * Typography variants for special use cases
 */
export const typographyVariants = [
  {
    name: 'stat' as const,
    style: (theme: Theme) => ({
      fontSize: '2.5rem',
      fontWeight: 700,
      lineHeight: 1.2,
      color: theme.palette.primary.main,
      [theme.breakpoints.down('sm')]: {
        fontSize: '2rem',
      },
    }),
  },
  {
    name: 'cardTitle' as const,
    style: (theme: Theme) => ({
      fontSize: '1.125rem',
      fontWeight: 600,
      lineHeight: 1.4,
      color: theme.palette.text.primary,
    }),
  },
  {
    name: 'cardSubtitle' as const,
    style: (theme: Theme) => ({
      fontSize: '0.875rem',
      fontWeight: 500,
      lineHeight: 1.4,
      color: theme.palette.text.secondary,
    }),
  },
  {
    name: 'overline' as const,
    style: (theme: Theme) => ({
      fontSize: '0.75rem',
      fontWeight: 600,
      lineHeight: 1.4,
      textTransform: 'uppercase' as const,
      letterSpacing: '0.05em',
      color: theme.palette.text.secondary,
    }),
  },
  {
    name: 'gradient' as const,
    style: (theme: Theme) => ({
      background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
      WebkitBackgroundClip: 'text',
      WebkitTextFillColor: 'transparent',
      backgroundClip: 'text',
      fontWeight: 700,
    }),
  },
];

/**
 * Avatar variants for different contexts
 */
export const avatarVariants = [
  {
    props: { variant: 'interactive' as const },
    style: (theme: Theme) => ({
      cursor: 'pointer',
      transition: theme.transitions.create(['transform', 'box-shadow'], {
        duration: theme.transitions.duration.short,
      }),
      '&:hover': {
        transform: 'scale(1.1)',
        boxShadow: `0 0 0 3px ${alpha(theme.palette.primary.main, 0.2)}`,
      },
    }),
  },
  {
    props: { variant: 'status' as const },
    style: (theme: Theme) => ({
      position: 'relative',
      '&::after': {
        content: '""',
        position: 'absolute',
        bottom: 0,
        right: 0,
        width: '25%',
        height: '25%',
        backgroundColor: theme.palette.success.main,
        borderRadius: '50%',
        border: `2px solid ${theme.palette.background.paper}`,
      },
    }),
  },
];

/**
 * Paper variants for different elevations and effects
 */
export const paperVariants = [
  {
    props: { variant: 'glass' as const },
    style: (theme: Theme) => ({
      backgroundColor: alpha(theme.palette.background.paper, 0.8),
      backdropFilter: 'blur(10px)',
      border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
    }),
  },
  {
    props: { variant: 'gradient' as const },
    style: (theme: Theme) => ({
      background: `linear-gradient(135deg, ${theme.palette.background.paper}, ${alpha(theme.palette.primary.main, 0.02)})`,
    }),
  },
];

// =============================================================================
// COMPONENT VARIANT COLLECTIONS
// =============================================================================

export const allVariants = {
  MuiButton: {
    variants: buttonVariants,
  },
  MuiCard: {
    variants: cardVariants,
  },
  MuiChip: {
    variants: chipVariants,
  },
  MuiAvatar: {
    variants: avatarVariants,
  },
  MuiPaper: {
    variants: paperVariants,
  },
};

// =============================================================================
// VARIANT HELPER FUNCTIONS
// =============================================================================

/**
 * Apply variants to theme components
 */
export const applyVariantsToTheme = (theme: Theme) => {
  return {
    ...theme,
    components: {
      ...theme.components,
      MuiButton: {
        ...theme.components?.MuiButton,
        variants: [
          ...(theme.components?.MuiButton?.variants || []),
          ...buttonVariants.map(variant => ({
            ...variant,
            style: variant.style(theme),
          })),
        ],
      },
      MuiCard: {
        ...theme.components?.MuiCard,
        variants: [
          ...(theme.components?.MuiCard?.variants || []),
          ...cardVariants.map(variant => ({
            ...variant,
            style: variant.style(theme),
          })),
        ],
      },
      MuiChip: {
        ...theme.components?.MuiChip,
        variants: [
          ...(theme.components?.MuiChip?.variants || []),
          ...chipVariants.map(variant => ({
            ...variant,
            style: variant.style(theme),
          })),
        ],
      },
      MuiAvatar: {
        ...theme.components?.MuiAvatar,
        variants: [
          ...(theme.components?.MuiAvatar?.variants || []),
          ...avatarVariants.map(variant => ({
            ...variant,
            style: variant.style(theme),
          })),
        ],
      },
      MuiPaper: {
        ...theme.components?.MuiPaper,
        variants: [
          ...(theme.components?.MuiPaper?.variants || []),
          ...paperVariants.map(variant => ({
            ...variant,
            style: variant.style(theme),
          })),
        ],
      },
      MuiTypography: {
        ...theme.components?.MuiTypography,
        variants: [
          ...(theme.components?.MuiTypography?.variants || []),
          ...typographyVariants.map(variant => ({
            props: { variant: variant.name },
            style: variant.style(theme),
          })),
        ],
      },
    },
  };
};
