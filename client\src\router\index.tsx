import React from 'react';
import { create<PERSON><PERSON>erRouter, Navigate, useNavigate } from 'react-router-dom';
import LandingPage from '../pages/LandingPage';
import Login from '../pages/Login';
import Dashboard from '../pages/Dashboard';
import { User } from '../types';

// Props interface for router context
export interface RouterContextProps {
  user: User | null;
  token: string | null;
  onLogin: (token: string, user: User) => void;
  onLogout: () => void;
}

// Wrapper components to handle navigation
const LandingPageWrapper: React.FC<{ user: User | null; token: string | null }> = () => {
  const navigate = useNavigate();

  const handleShowLogin = () => {
    navigate('/login');
  };

  return <LandingPage onShowLogin={handleShowLogin} />;
};

const LoginWrapper: React.FC<{ onLogin: (token: string, user: User) => void }> = ({ onLogin }) => {
  const navigate = useNavigate();

  const handleLogin = (token: string, user: User) => {
    onLogin(token, user);
    navigate('/dashboard');
  };

  const handleBack = () => {
    navigate('/');
  };

  return <Login onLogin={handleLogin} onBack={handleBack} />;
};

// Protected Route Component
interface ProtectedRouteProps {
  children: React.ReactNode;
  user: User | null;
  token: string | null;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, user, token }) => {
  if (!token || !user) {
    return <Navigate to="/login" replace />;
  }
  return <>{children}</>;
};

// Public Route Component (redirects to dashboard if authenticated)
interface PublicRouteProps {
  children: React.ReactNode;
  user: User | null;
  token: string | null;
}

const PublicRoute: React.FC<PublicRouteProps> = ({ children, user, token }) => {
  if (token && user) {
    return <Navigate to="/dashboard" replace />;
  }
  return <>{children}</>;
};

// Create router configuration function
export const createAppRouter = (context: RouterContextProps) => {
  return createBrowserRouter([
    {
      path: '/',
      element: (
        <PublicRoute user={context.user} token={context.token}>
          <LandingPageWrapper user={context.user} token={context.token} />
        </PublicRoute>
      ),
    },
    {
      path: '/login',
      element: (
        <PublicRoute user={context.user} token={context.token}>
          <LoginWrapper onLogin={context.onLogin} />
        </PublicRoute>
      ),
    },
    {
      path: '/dashboard',
      element: (
        <ProtectedRoute user={context.user} token={context.token}>
          <Dashboard user={context.user} onLogout={context.onLogout} />
        </ProtectedRoute>
      ),
    },
    {
      path: '*',
      element: <Navigate to="/" replace />,
    },
  ]);
};

export { ProtectedRoute, PublicRoute };
