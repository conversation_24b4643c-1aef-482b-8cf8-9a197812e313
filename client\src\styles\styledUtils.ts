import { styled, Theme } from '@mui/material/styles';
import { Box, Card, CardContent, Typography } from '@mui/material';

// =============================================================================
// UTILITY TYPES
// =============================================================================

export interface SpacingProps {
  spacing?: number | string;
  gap?: number | string;
}

export interface VariantProps {
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
}

export interface PriorityProps {
  priority?: 'low' | 'medium' | 'high';
}

export interface SizeProps {
  size?: 'small' | 'medium' | 'large';
}

// =============================================================================
// LAYOUT UTILITIES
// =============================================================================

/**
 * Flexible container with consistent padding and spacing
 */
export const StyledContainer = styled(Box, {
  shouldForwardProp: (prop) => !['spacing', 'gap'].includes(prop as string),
})<SpacingProps>(({ theme, spacing = 3, gap }) => ({
  padding: theme.spacing(spacing),
  ...(gap && {
    display: 'flex',
    flexDirection: 'column',
    gap: theme.spacing(typeof gap === 'string' ? parseInt(gap) : gap),
  }),
}));

/**
 * Flex container with customizable direction and spacing
 */
export const FlexContainer = styled(Box, {
  shouldForwardProp: (prop) => !['direction', 'justify', 'align', 'gap', 'wrap'].includes(prop as string),
})<{
  direction?: 'row' | 'column';
  justify?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
  align?: 'flex-start' | 'center' | 'flex-end' | 'stretch';
  gap?: number;
  wrap?: boolean;
}>(({ theme, direction = 'row', justify = 'flex-start', align = 'stretch', gap = 0, wrap = false }) => ({
  display: 'flex',
  flexDirection: direction,
  justifyContent: justify,
  alignItems: align,
  gap: gap ? theme.spacing(gap) : 0,
  flexWrap: wrap ? 'wrap' : 'nowrap',
}));

/**
 * Grid container with responsive columns
 */
export const GridContainer = styled(Box, {
  shouldForwardProp: (prop) => !['columns', 'gap', 'minWidth'].includes(prop as string),
})<{
  columns?: { xs?: number; sm?: number; md?: number; lg?: number };
  gap?: number;
  minWidth?: string;
}>(({ theme, columns = { xs: 1, sm: 2, md: 3, lg: 4 }, gap = 2, minWidth }) => ({
  display: 'grid',
  gap: theme.spacing(gap),
  gridTemplateColumns: `repeat(${columns.xs || 1}, 1fr)`,
  ...(minWidth && {
    gridTemplateColumns: `repeat(auto-fit, minmax(${minWidth}, 1fr))`,
  }),
  [theme.breakpoints.up('sm')]: {
    gridTemplateColumns: `repeat(${columns.sm || columns.xs || 1}, 1fr)`,
  },
  [theme.breakpoints.up('md')]: {
    gridTemplateColumns: `repeat(${columns.md || columns.sm || columns.xs || 1}, 1fr)`,
  },
  [theme.breakpoints.up('lg')]: {
    gridTemplateColumns: `repeat(${columns.lg || columns.md || columns.sm || columns.xs || 1}, 1fr)`,
  },
}));

// =============================================================================
// CARD UTILITIES
// =============================================================================

/**
 * Enhanced card with priority styling and hover effects
 */
export const StyledCard = styled(Card, {
  shouldForwardProp: (prop) => !['priority', 'interactive', 'selected'].includes(prop as string),
})<PriorityProps & { interactive?: boolean; selected?: boolean }>(({ theme, priority, interactive, selected }) => ({
  transition: theme.transitions.create(['transform', 'box-shadow', 'border-color'], {
    duration: theme.transitions.duration.short,
  }),
  ...(priority && {
    borderLeft: `4px solid ${
      priority === 'high' 
        ? theme.palette.error.main
        : priority === 'medium'
        ? theme.palette.warning.main
        : theme.palette.success.main
    }`,
  }),
  ...(selected && {
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.action.selected,
  }),
  ...(interactive && {
    cursor: 'pointer',
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: theme.shadows[4],
    },
  }),
}));

/**
 * Stat card for dashboard metrics
 */
export const StatCard = styled(Card)(({ theme }) => ({
  height: '100%',
  transition: theme.transitions.create(['transform', 'box-shadow'], {
    duration: theme.transitions.duration.short,
  }),
  '&:hover': {
    transform: 'translateY(-1px)',
    boxShadow: theme.shadows[3],
  },
}));

export const StatCardContent = styled(CardContent)(({ theme }) => ({
  textAlign: 'center',
  padding: theme.spacing(3),
  '&:last-child': {
    paddingBottom: theme.spacing(3),
  },
}));

// =============================================================================
// TYPOGRAPHY UTILITIES
// =============================================================================

/**
 * Enhanced typography with variant-based styling
 */
export const StyledTypography = styled(Typography, {
  shouldForwardProp: (prop) => !['colorVariant'].includes(prop as string),
})<{ colorVariant?: VariantProps['variant'] }>(({ theme, colorVariant }) => ({
  ...(colorVariant && {
    color: theme.palette[colorVariant].main,
  }),
}));

/**
 * Icon typography for emoji icons with consistent sizing
 */
export const IconTypography = styled(Typography, {
  shouldForwardProp: (prop) => !['size'].includes(prop as string),
})<SizeProps>(({ size = 'medium' }) => ({
  fontSize: size === 'small' ? '1.5rem' : size === 'large' ? '4rem' : '2rem',
  lineHeight: 1,
  display: 'block',
}));

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Get priority color from theme
 */
export const getPriorityColor = (theme: Theme, priority: string) => {
  switch (priority) {
    case 'high':
      return theme.palette.error.main;
    case 'medium':
      return theme.palette.warning.main;
    case 'low':
      return theme.palette.success.main;
    default:
      return theme.palette.text.secondary;
  }
};

/**
 * Create responsive spacing values
 */
export const createResponsiveSpacing = (theme: Theme, xs: number, sm?: number, md?: number, lg?: number) => ({
  padding: theme.spacing(xs),
  [theme.breakpoints.up('sm')]: {
    padding: theme.spacing(sm || xs),
  },
  [theme.breakpoints.up('md')]: {
    padding: theme.spacing(md || sm || xs),
  },
  [theme.breakpoints.up('lg')]: {
    padding: theme.spacing(lg || md || sm || xs),
  },
});

/**
 * Create hover effect styles
 */
export const createHoverEffect = (theme: Theme, intensity: 'subtle' | 'medium' | 'strong' = 'medium') => {
  const transforms = {
    subtle: 'translateY(-1px)',
    medium: 'translateY(-2px)',
    strong: 'translateY(-4px)',
  };
  
  const shadows = {
    subtle: theme.shadows[2],
    medium: theme.shadows[4],
    strong: theme.shadows[8],
  };

  return {
    transition: theme.transitions.create(['transform', 'box-shadow'], {
      duration: theme.transitions.duration.short,
    }),
    '&:hover': {
      transform: transforms[intensity],
      boxShadow: shadows[intensity],
    },
  };
};

/**
 * Create focus ring styles
 */
export const createFocusRing = (theme: Theme, color?: string) => ({
  '&:focus-visible': {
    outline: `2px solid ${color || theme.palette.primary.main}`,
    outlineOffset: '2px',
  },
});

// =============================================================================
// COMMON STYLED COMPONENTS
// =============================================================================

/**
 * Section wrapper with consistent spacing
 */
export const Section = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  [theme.breakpoints.up('md')]: {
    padding: theme.spacing(4),
  },
}));

/**
 * Page header with title and actions
 */
export const PageHeader = styled(FlexContainer)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  [theme.breakpoints.down('sm')]: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: theme.spacing(2),
  },
}));

/**
 * Empty state container
 */
export const EmptyState = styled(Box)(({ theme }) => ({
  textAlign: 'center',
  padding: theme.spacing(6, 3),
  color: theme.palette.text.secondary,
}));
