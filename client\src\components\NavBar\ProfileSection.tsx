import React from 'react';
import { styled } from '@mui/material/styles';
import { Box, Avatar, IconButton } from '@mui/material';
import { Menu as MenuIcon } from '@mui/icons-material';

// Styled Components
const ProfileSectionContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
}));

const MobileMenuButton = styled(IconButton)(({ theme }) => ({
  color: 'white',
  display: 'none',
  [theme.breakpoints.down('md')]: {
    display: 'flex',
  },
}));

const ProfileAvatar = styled(Avatar)(({ theme }) => ({
  width: 36,
  height: 36,
  backgroundColor: 'rgba(255, 255, 255, 0.2)',
  border: '2px solid rgba(255, 255, 255, 0.3)',
  cursor: 'pointer',
  transition: theme.transitions.create(['transform', 'box-shadow'], {
    duration: theme.transitions.duration.short,
  }),
  '&:hover': {
    transform: 'scale(1.05)',
    boxShadow: '0 4px 12px rgba(255, 255, 255, 0.3)',
  },
}));

// Component Props Interface
interface ProfileSectionProps {
  onProfileClick?: () => void;
  onMobileMenuClick?: () => void;
  profileImageSrc?: string;
  profileAltText?: string;
}

const ProfileSection: React.FC<ProfileSectionProps> = ({ 
  onProfileClick, 
  onMobileMenuClick,
  profileImageSrc = "/Pigeon Squad Logo.png",
  profileAltText = "Profile"
}) => {
  return (
    <ProfileSectionContainer>
      <MobileMenuButton onClick={onMobileMenuClick}>
        <MenuIcon />
      </MobileMenuButton>
      <ProfileAvatar onClick={onProfileClick}>
        <img src={profileImageSrc} alt={profileAltText} />
      </ProfileAvatar>
    </ProfileSectionContainer>
  );
};

export default ProfileSection;
