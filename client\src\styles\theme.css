/*
 * CSS Custom Properties for MUI Theme Integration
 * These variables align with the MUI theme configuration
 * and can be used for CSS-in-JS interoperability
 */
:root {
  /* Primary Colors - New palette: #02649c */
  --mui-primary-main: #02649c;
  --mui-primary-light: #4a8bc2;
  --mui-primary-dark: #01456d;

  /* Secondary Colors - New palette: #0074b2 */
  --mui-secondary-main: #0074b2;
  --mui-secondary-light: #4a96c4;
  --mui-secondary-dark: #00517c;

  /* Background Colors */
  --mui-background-default: #F8FAFC;
  --mui-background-paper: #ffffff;

  /* Text Colors */
  --mui-text-primary: #1E293B;
  --mui-text-secondary: #64748B;

  /* Status Colors - Updated with new palette */
  --mui-success-main: #57e2e0; /* Quaternary color */
  --mui-warning-main: #fde587; /* Accent color */
  --mui-error-main: #a480f9;   /* Highlights color */
  --mui-info-main: #2ebdf9;    /* Tertiary color */

  /* Extended Shadows - Aligned with theme extensions */
  --mui-shadow-card: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --mui-shadow-card-hover: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --mui-shadow-elevated: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Border Radius - Aligned with MUI theme */
  --mui-border-radius: 8px;

  /* Spacing - Aligned with MUI theme (8px base) */
  --mui-spacing-1: 8px;
  --mui-spacing-2: 16px;
  --mui-spacing-3: 24px;
  --mui-spacing-4: 32px;
  --mui-spacing-6: 48px;
  --mui-spacing-8: 64px;

  /* Typography - Aligned with MUI theme */
  --mui-font-family: "Inter", "Roboto", "Helvetica", "Arial", sans-serif;
}

/*
 * Utility classes for CSS-in-JS interoperability
 * These are minimal and should be used sparingly
 * Prefer MUI styled components over these utilities
 */

/* Animation keyframes for styled components */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Utility classes for special cases */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

/*
 * Note: This file is kept minimal for CSS-in-JS interoperability.
 * Most styling should be done through MUI styled components.
 * These CSS custom properties can be used in edge cases where
 * direct CSS access is needed.
 */