import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  App<PERSON>ar, 
  Toolbar, 
  Typography, 
  Button, 
  Box, 
  Avatar,
  Breadcrumbs,
  Link
} from '@mui/material';
import { AccountCircle, Home, Dashboard as DashboardIcon, Login } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { User } from '../types';

interface NavigationProps {
  user: User | null;
  onLogout?: () => void;
  showBreadcrumbs?: boolean;
}

const Navigation: React.FC<NavigationProps> = ({ user, onLogout, showBreadcrumbs = false }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  const handleLogout = () => {
    if (onLogout) {
      onLogout();
    }
    navigate('/');
  };

  const getBreadcrumbs = () => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbs = [
      { label: t('nav.home'), path: '/', icon: <Home fontSize="small" /> }
    ];

    if (pathSegments.includes('dashboard')) {
      breadcrumbs.push({ 
        label: t('nav.dashboard'), 
        path: '/dashboard', 
        icon: <DashboardIcon fontSize="small" /> 
      });
    }

    if (pathSegments.includes('login')) {
      breadcrumbs.push({ 
        label: t('nav.login'), 
        path: '/login', 
        icon: <Login fontSize="small" /> 
      });
    }

    return breadcrumbs;
  };

  return (
    <>
      <AppBar position="static">
        <Toolbar>
          <Box 
            component="img" 
            src="/Pigeon Squad Logo.png" 
            alt="Pigeon Squad" 
            sx={{ width: 32, height: 32, mr: 1, cursor: 'pointer' }}
            onClick={() => handleNavigation('/')}
          />
          <Typography 
            variant="h6" 
            component="div" 
            sx={{ flexGrow: 1, cursor: 'pointer' }}
            onClick={() => handleNavigation('/')}
          >
            {t('app.title')}
          </Typography>
          
          <Box display="flex" alignItems="center" gap={2}>
            {user ? (
              <>
                <Typography variant="body2">
                  {t('app.welcome', { name: user.name || user.email })}
                </Typography>
                <Avatar sx={{ width: 32, height: 32 }}>
                  <AccountCircle />
                </Avatar>
                <Button color="inherit" onClick={handleLogout}>
                  {t('auth.logout')}
                </Button>
              </>
            ) : (
              <Button color="inherit" onClick={() => handleNavigation('/login')}>
                {t('auth.signIn')}
              </Button>
            )}
          </Box>
        </Toolbar>
      </AppBar>

      {showBreadcrumbs && (
        <Box sx={{ p: 2, bgcolor: 'background.paper' }}>
          <Breadcrumbs aria-label="breadcrumb">
            {getBreadcrumbs().map((crumb) => (
              <Link
                key={crumb.path}
                color="inherit"
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  handleNavigation(crumb.path);
                }}
                sx={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  gap: 0.5,
                  textDecoration: 'none',
                  '&:hover': { textDecoration: 'underline' }
                }}
              >
                {crumb.icon}
                {crumb.label}
              </Link>
            ))}
          </Breadcrumbs>
        </Box>
      )}
    </>
  );
};

export default Navigation;
